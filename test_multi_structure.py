#!/usr/bin/env python3

import duckdb
import json
import os

# Create test data with multiple arrays and mixed structures
test_data = {
    "metadata": {"version": "1.0", "created": "2024-01-01"},
    "users": [
        {"id": 1, "name": "<PERSON>", "email": "<EMAIL>"},
        {"id": 2, "name": "<PERSON>", "email": "<EMAIL>"}
    ],
    "products": [
        {"id": 101, "title": "Widget", "price": 19.99},
        {"id": 102, "title": "Gadget", "price": 29.99}
    ],
    "summary": {"total_users": 2, "total_products": 2}
}

# Write test data to file
with open("test_multi.json", "w") as f:
    json.dump(test_data, f, indent=2)

# Test the extension
try:
    # Create connection with unsigned extensions enabled
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    
    # Load the extension
    conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
    print("✅ Extension loaded successfully")
    
    # Test what schema is discovered
    print("\n🔍 Testing multi-structure JSON schema discovery:")
    result = conn.execute("SELECT * FROM streaming_json_reader('test_multi.json')").fetchall()
    print(f"Result: {result}")
    
    # Test specific column selection
    print("\n🔍 Testing specific column selection:")
    result = conn.execute("SELECT name FROM streaming_json_reader('test_multi.json')").fetchall()
    print(f"Names only: {result}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

finally:
    # Clean up
    if os.path.exists("test_multi.json"):
        os.remove("test_multi.json")
