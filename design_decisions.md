# Design Decisions and Functionality Approaches

This document captures all major design decisions and functionality approaches made during the development of the Streaming JSON Reader Extension for DuckDB.

## 1. Core Architecture Decisions

### 1.1 Streaming vs Traditional JSON Processing
**Decision**: Use streaming JSON parsing with the `struson` crate instead of loading entire JSON objects into memory.

**Rationale**:
- Traditional approach: O(file_size) memory usage
- Streaming approach: O(row_size) memory usage
- Enables processing of files larger than available RAM
- Prevents out-of-memory failures on large datasets

### 1.2 Language Choice: Rust
**Decision**: Implement the extension in Rust using DuckDB's C API bindings.

**Rationale**:
- Memory safety guarantees crucial for database extensions
- Performance characteristics suitable for streaming operations
- Strong ecosystem for JSON parsing (struson crate)
- FFI capabilities for DuckDB integration
- User preference for Rust development

**Trade-offs**:
- More complex FFI boundary management
- Steeper learning curve for DuckDB extension development
- Limited documentation compared to C++ extensions

## 2. JSON Path Navigation Strategy

### 2.1 Automatic Path Inference
**Decision**: Implement automatic JSON path inference based on query column patterns rather than requiring explicit path parameters.

**Rationale**:
- Reduces user configuration burden
- Enables "just works" experience: `SELECT * FROM streaming_json_reader('file.json')`
- Leverages DuckDB's projection pushdown capabilities
- More intuitive API design

### 2.2 Context Preservation for Nested Structures
**Decision**: Maintain parent object context when flattening nested arrays (e.g., user info in project rows).

Follow the way duckdb handles `select a, unnest(b)` where `a` values are repeated for each unnested row of `b`.

## 4. Error Handling Philosophy

### 4.1 Fail-Fast Error Propagation
**Decision**: Stop processing and return errors immediately on malformed JSON rather than attempting partial recovery.

**Rationale**:
- Maintains transactional semantics
- Prevents silent data corruption
- Simpler error handling logic
- Clear failure modes for debugging

**Trade-off**: No partial results on parse errors vs data integrity guarantees

## 5. Performance Optimization Decisions

### 5.1 Batch Processing Limits
**Decision**: Implement 100-element limits per array level to prevent memory exhaustion. Much better option: base it on the row group size from duckdb's query information if available.

**Rationale**:
- Prevents pathological cases with extremely large arrays
- Maintains predictable memory usage
- Balances throughput with resource constraints

## 6. API Design Principles (User-Driven Decisions)

### 6.1 Query-Driven JSON Structure Inference
**Decision**: JSON structure should ideally be inferred from the query structure, e.g. an `unnest(x)` should assume x is some kind of nested field then unnest it into rows.

The JSON structure should ideally be inferred from the query structure, e.g. an unnest(x) should assume s is some kind of nested field then unnest it into rows... This may or may not be possible and is an entire topic of its own to research in duckdb's extension interface.

### 6.2 SELECT * Behavior - Match Default DuckDB JSON Reader
**Decision**: `SELECT *` will return exactly the same as you get with the default DuckDB JSON reader

### 6.4 Streaming Validation via Struson
Validate structure as we parse, rely entirely on struson for JSON validation. It will raise errors which we need to handle and convert to duckdb errors.

### 6.5 Fail-Fast Error Handling
**Decision**: Fail on errors to begin with, this is simplest. Any clever error handling or failure recovery can wait until later.

**Rationale**: Avoid complexity until basic functionality is solid

### 6.7 No Hard-coded JSON Structure Assumptions
**Decision**: Extension should work with any JSON structure, not just user/project schemas. Make it truly generic.

### 7.4 Predicate Pushdown for Path Inference
**Decision**: Explore predicate pushdown for automatic path inference instead of requiring explicit path parameters.

**Research Area**: Integration with DuckDB's query optimizer for automatic path detection

### 7.5 True Streaming Without Full File Pre-validation
**Decision**: Implement true streaming without full file pre-validation, avoiding loading entire JSON objects into memory.

## 8. Development Workflow Decisions (User-Driven)

### 8.1 Python Package Management
**Decision**: Use uv for Python package management.

### 8.2 Incremental Git Commits
**Decision**: Make incremental git commits.

### 8.3 General, Reusable Package Design
**Decision**: Design for general, reusable package and consult user when multiple design options exist rather than making decisions unilaterally.

### 8.4 Test Code Organization
**Decision**: Organize test code in separate dedicated folders rather than mixed with main code.

**User Input**: "User prefers test code organized in separate dedicated folders rather than mixed with main code"

**Implementation**: Separate test directories and files

### 8.5 Design Decision Documentation
**Decision**: Document design decisions and functionality approaches in a 'design_decisions.md' file for future reference.

**User Input**: "User wants design decisions and functionality approaches documented in a 'design_decisions.md' file for future reference"

**Purpose**: Maintain clear record of architectural choices and rationale

## 9. Testing Strategy Decisions

### 9.1 Pytest Structure for Reusability
**Decision**: Implement comprehensive pytest test suite with reusable fixtures and test data generation.

**Rationale**:
- User preference for pytest structure
- Enables automated regression testing
- Provides foundation for continuous integration
- Facilitates debugging and development iteration

**Implementation**: 9 test cases covering functionality, memory efficiency, and edge cases

### 7.2 Memory Efficiency Validation
**Decision**: Include comparative memory testing against DuckDB's default JSON reader.

**Rationale**:
- Validates core value proposition of the extension
- Provides concrete performance metrics
- Enables regression detection for memory usage

## 8. Incremental Development Approach

### 8.1 Feature Progression Strategy
**Decision**: Build functionality incrementally: basic reading → array flattening → nested paths → path inference.

**Rationale**:
- User preference for incremental git commits
- Enables early validation of core concepts
- Reduces risk of complex integration issues
- Facilitates debugging and testing

**Progression**:
1. Basic JSON file reading
2. Array flattening functionality  
3. Multiply-nested path support
4. Automatic path inference
5. Comprehensive testing

### 8.2 Commit Strategy
**Decision**: Make detailed, incremental commits with comprehensive descriptions.

**Rationale**:
- User preference for incremental git commits
- Facilitates code review and debugging
- Provides clear development history
- Enables selective rollback if needed

## 9. Future Architecture Considerations

### 9.1 Projection Pushdown Integration
**Decision**: Implement framework for projection pushdown but acknowledge limitations with current DuckDB Rust bindings.

**Rationale**:
- Prepares for future API improvements
- Demonstrates understanding of optimization opportunities
- Provides foundation for enhanced performance

**Current Status**: Framework exists, full integration pending API availability

### 9.2 Extensibility Design
**Decision**: Design with future enhancements in mind (configurable paths, type preservation, parallel processing).

**Rationale**:
- Anticipates user needs for production deployment
- Provides clear upgrade path
- Maintains architectural flexibility

## 10. Documentation Philosophy

### 10.1 Comprehensive Technical Documentation
**Decision**: Create detailed README with performance characteristics, edge cases, and limitations.

**Rationale**:
- User request for detailed documentation
- Enables informed deployment decisions
- Facilitates maintenance and debugging
- Provides foundation for external assessment

### 10.2 Critical Assessment Framework
**Decision**: Provide structured methodology for external evaluation and testing.

**Rationale**:
- Acknowledges limitations and areas for improvement
- Enables thorough security and performance evaluation
- Demonstrates commitment to production readiness
- Facilitates community contribution and feedback

---

**Key Principles Throughout Development**:
- Memory efficiency as primary goal
- User experience simplicity
- Incremental, testable development
- Honest assessment of limitations
- Future-oriented architecture design
