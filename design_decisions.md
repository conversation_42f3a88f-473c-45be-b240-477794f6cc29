# Design Decisions and Functionality Approaches

This document captures all major design decisions and functionality approaches made during the development of the Streaming JSON Reader Extension for DuckDB.

## 1. Core Architecture Decisions

### 1.1 Streaming vs Traditional JSON Processing
**Decision**: Use streaming JSON parsing with the `struson` crate instead of loading entire JSON objects into memory.

**Rationale**:
- Traditional approach: O(file_size) memory usage
- Streaming approach: O(row_size) memory usage
- Enables processing of files larger than available RAM
- Prevents out-of-memory failures on large datasets

### 1.2 Language Choice: Rust
**Decision**: Implement the extension in Rust using DuckDB's C API bindings.

**Rationale**:
- Memory safety guarantees crucial for database extensions
- Performance characteristics suitable for streaming operations
- Strong ecosystem for JSON parsing (struson crate)
- FFI capabilities for DuckDB integration
- User preference for Rust development

**Trade-offs**:
- More complex FFI boundary management
- Steeper learning curve for DuckDB extension development
- Limited documentation compared to C++ extensions

## 2. JSON Path Navigation Strategy

### 2.1 Automatic Path Inference
**Decision**: Implement automatic JSON path inference based on query column patterns rather than requiring explicit path parameters.

**Rationale**:
- Reduces user configuration burden
- Enables "just works" experience: `SELECT * FROM streaming_json_reader('file.json')`
- Leverages DuckDB's projection pushdown capabilities
- More intuitive API design

### 2.2 Context Preservation for Nested Structures
**Decision**: Maintain parent object context when flattening nested arrays (e.g., user info in project rows).

Follow the way duckdb handles `select a, unnest(b)` where `a` values are repeated for each unnested row of `b`.

## 3. Error Handling Philosophy

### 3.1 Fail-Fast Error Propagation
**Decision**: Stop processing and return errors immediately on malformed JSON rather than attempting partial recovery.

**Rationale**:
- Maintains transactional semantics
- Prevents silent data corruption
- Simpler error handling logic
- Clear failure modes for debugging

**Trade-off**: No partial results on parse errors vs data integrity guarantees

## 4. Performance Optimization Decisions

### 4.1 Batch Processing Limits
**Decision**: Implement 100-element limits per array level to prevent memory exhaustion. Much better option: base it on the row group size from duckdb's query information if available.

**Rationale**:
- Prevents pathological cases with extremely large arrays
- Maintains predictable memory usage
- Balances throughput with resource constraints

## 5. API Design Principles (User-Driven Decisions)

### 5.1 Query-Driven JSON Structure Inference
**Decision**: JSON structure should ideally be inferred from the query structure, e.g. an `unnest(x)` should assume x is some kind of nested field then unnest it into rows.

The JSON structure should ideally be inferred from the query structure, e.g. an unnest(x) should assume s is some kind of nested field then unnest it into rows... This may or may not be possible and is an entire topic of its own to research in duckdb's extension interface.

### 5.2 SELECT * Behavior - Match Default DuckDB JSON Reader
**Decision**: `SELECT *` will return exactly the same as you get with the default DuckDB JSON reader

### 5.3 Streaming Validation via Struson
Validate structure as we parse, rely entirely on struson for JSON validation. It will raise errors which we need to handle and convert to duckdb errors.

### 5.4 Fail-Fast Error Handling
**Decision**: Fail on errors to begin with, this is simplest. Any clever error handling or failure recovery can wait until later.

**Rationale**: Avoid complexity until basic functionality is solid

### 5.5 No Hard-coded JSON Structure Assumptions
**Decision**: Extension should work with any JSON structure, not just user/project schemas. Make it truly generic.

## 6. Advanced Features

### 6.1 Predicate Pushdown for Path Inference
**Decision**: Explore predicate pushdown for automatic path inference instead of requiring explicit path parameters.

**Research Area**: Integration with DuckDB's query optimizer for automatic path detection

### 6.2 True Streaming Without Full File Pre-validation
**Decision**: Implement true streaming without full file pre-validation, avoiding loading entire JSON objects into memory.

## 7. Development Workflow Decisions (User-Driven)

### 7.1 Python Package Management
**Decision**: Use uv for Python package management.

### 7.2 Incremental Git Commits
**Decision**: Make incremental git commits.

### 7.3 General, Reusable Package Design
**Decision**: Design for general, reusable package and consult user when multiple design options exist rather than making decisions unilaterally.

### 7.4 Test Code Organization
**Decision**: Organize test code in separate dedicated folders rather than mixed with main code.

### 7.5 Design Decision Documentation
**Decision**: Document design decisions and functionality approaches in a 'design_decisions.md' file for future reference.

### 7.6 Memory Efficiency Validation
**Decision**: Include comparative memory testing against DuckDB's default JSON reader.

**Rationale**:
- Validates core value proposition of the extension
- Provides concrete performance metrics
- Enables regression detection for memory usage

## 8. Incremental Development Approach

### 8.1 Feature Progression Strategy
**Decision**: Build functionality incrementally: basic reading → array flattening → nested paths → path inference.

**Rationale**:
- User preference for incremental git commits
- Enables early validation of core concepts
- Reduces risk of complex integration issues
- Facilitates debugging and testing

**Progression**:
1. Basic JSON file reading
2. Array flattening functionality
3. Multiply-nested path support
4. Automatic path inference
5. Comprehensive testing

## 9. Current Project Assessment (June 2025)

### 9.1 Project State Analysis
**Current Status**: Early prototype with functional core but significant regression in latest commits.

**Working Version**: Commit 28c4864 demonstrates successful implementation of core streaming functionality:
- ✅ Proper column structure with meaningful names
- ✅ Nested path flattening (users.projects → flat rows)
- ✅ Context preservation (user info replicated in project rows)
- ✅ Streaming JSON parsing with struson
- ❌ Hardcoded to specific JSON structure ("users.projects")
- ❌ No generic path inference

**Latest Version Issues**: Commit c00f6dd and later introduced major regressions:
- ❌ Returns raw JSON strings instead of structured data
- ❌ Lost all streaming and flattening functionality
- ❌ No longer meets core design objectives

### 9.2 Gap Analysis Against Design Decisions

**Critical Gaps**:
1. **Generic JSON Structure Support** (Decision 5.5): Current implementation hardcoded to users/projects
2. **SELECT * Behavior** (Decision 5.2): Does not match DuckDB's default JSON reader behavior
3. **Automatic Path Inference** (Decision 2.1): Not implemented
4. **Query-Driven Structure Inference** (Decision 5.1): Not implemented

**Partially Implemented**:
1. **Streaming Architecture** (Decision 1.1): Core concept proven but needs generalization
2. **Context Preservation** (Decision 2.2): Works for hardcoded case
3. **Fail-Fast Error Handling** (Decision 3.1): Basic implementation exists

**Well Implemented**:
1. **Rust Language Choice** (Decision 1.2): Solid foundation
2. **Incremental Development** (Decision 8.1): Good git history showing progression

## 10. Detailed Development Plan

### 10.1 Immediate Actions (Phase 1: Foundation Recovery)
**Objective**: Restore and generalize the working functionality from commit 28c4864

**Tasks**:
1. **Revert to Working Base**: Start from commit 28c4864 as the foundation
2. **Remove Hardcoded Assumptions**: Extract the users.projects logic into generic path handling
3. **Implement Generic Column Discovery**: Analyze JSON structure to determine column names dynamically
4. **Add Path Parameter Support**: Enable `streaming_json_reader('file.json', 'path.to.array')`

**Success Criteria**:
- Extension works with any JSON structure, not just users/projects
- Supports configurable JSON paths via second parameter
- Maintains streaming memory efficiency
- Preserves context for nested structures

### 10.2 Core Functionality Implementation (Phase 2: API Compliance)
**Objective**: Implement SELECT * behavior matching DuckDB's default JSON reader

**Research Questions for User**:
1. **Schema Discovery Strategy**: How should we handle JSON files with varying schemas across array elements?
   - Option A: Use union of all fields found in first N elements
   - Option B: Scan entire file once to discover complete schema
   - Option C: Use first element schema and fill missing fields with nulls

2. **Type Inference Approach**: How should we handle JSON type preservation?
   - Option A: Keep everything as VARCHAR initially (current approach)
   - Option B: Infer types from first few elements (numbers, booleans, etc.)
   - Option C: Use DuckDB's JSON type system for complex nested structures

3. **Large Array Handling**: How should we handle arrays with millions of elements?
   - Option A: Process in configurable batches (current 100-element limit)
   - Option B: Use DuckDB's row group size for batching
   - Option C: Stream continuously with memory pressure monitoring

**Tasks**:
1. **Implement Schema Discovery**: Analyze JSON structure to create appropriate DuckDB columns
2. **Add Type Inference**: Convert JSON types to appropriate DuckDB types
3. **Implement SELECT * Compatibility**: Match behavior of `read_json_auto()`
4. **Add Comprehensive Error Handling**: Proper DuckDB error integration

### 10.3 Advanced Features (Phase 3: Optimization)
**Objective**: Implement automatic path inference and query-driven optimization

**Research Questions for User**:
1. **Path Inference Strategy**: How aggressive should automatic path inference be?
   - Option A: Conservative - only flatten obvious array structures
   - Option B: Aggressive - attempt to flatten any nested arrays found
   - Option C: Query-driven - analyze projected columns to infer desired structure

2. **Performance vs Memory Trade-offs**: What's the priority for large files?
   - Option A: Minimize memory usage (current approach)
   - Option B: Optimize for query performance (larger batches)
   - Option C: Adaptive based on available system memory

**Tasks**:
1. ✅ **Research DuckDB Extension API**: Complete - projection pushdown via `ColumnIndex` system
2. **Implement Path Inference**: Use `column_indexes` for automatic field detection
3. **Add Performance Optimizations**: Batch size tuning, parallel processing
4. **Comprehensive Testing**: Memory efficiency validation, large file testing

### 10.4 Production Readiness (Phase 4: Polish)
**Objective**: Ensure extension is production-ready with comprehensive testing

**Tasks**:
1. **Security Hardening**: Input validation, resource limits, error boundaries
2. **Performance Benchmarking**: Comparative testing vs default JSON reader
3. **Documentation**: User guide, API reference, performance characteristics
4. **Integration Testing**: Compatibility with various DuckDB features

## 11. Finalized Design Decisions (June 2025)

### 11.1 Schema Discovery Strategy
**Decision**: Use first element schema only, with query-driven column selection.

**Rationale**:
- Scanning ahead may be slow for very large objects
- Users can implicitly define schema by selecting named fields (e.g., `SELECT age, name FROM ...`)
- Avoids performance penalty of full file scanning
- Enables streaming without lookahead

**Implementation**: Extract schema from first array element, allow query projection to drive column selection

### 11.2 Type System Integration
**Decision**: Work towards full DuckDB JSON type compatibility, use VARCHAR for intermediate versions.

**Rationale**:
- Should eventually match DuckDB's existing JSON reader API
- Full types including nested types is the end goal
- VARCHAR acceptable for initial implementations

**Implementation**: Start with VARCHAR, progressively add type inference

### 11.3 Memory Management Strategy
**Decision**: Adaptive batching based on DuckDB's vector sizes and available memory.

**Rationale**:
- Most "DuckDB way" of handling memory
- DuckDB already specifies vector sizes for memory storage
- When choosing, optimize for low memory usage
- Leverage existing DuckDB memory management

**Implementation**: Use DuckDB's vector size hints, fall back to conservative batching

### 11.4 API Surface Design
**Decision**: Single parameter API - `streaming_json_reader(file_path)` with query-driven behavior.

**Rationale**:
- `SELECT *` should match DuckDB's `read_json_auto()` behavior exactly
- Context-preserving flattening via query structure (e.g., `SELECT name, unnest(data)`)
- Query planner integration determines streaming vs full structure return
- No explicit path parameters - behavior driven by query analysis

**Implementation**: Single parameter function, behavior determined by query planner integration

### 11.5 Query-Driven Path Inference ✅ RESEARCH COMPLETE
**Decision**: Use DuckDB's `ColumnIndex` system for nested field projection pushdown.

**Research Results**:
- ✅ DuckDB provides complete nested field projection through `ColumnIndex` structure
- ✅ Table functions can access this via `TableFunctionInitInput.column_indexes`
- ✅ Parquet reader provides exact implementation pattern to follow
- ✅ Recursive nested field access fully supported

**Implementation Approach**:
1. Set `projection_pushdown = true` in table function definition
2. Process `input.column_indexes` to determine required JSON fields
3. Implement recursive field selection similar to parquet reader
4. Stream only projected JSON paths, avoiding unnecessary parsing

**Status**: Ready for implementation - no fallback approach needed

## 12. Query Planner Integration Research Results ✅

### 12.1 COMPLETED: DuckDB Nested Field Projection System

**Research Status**: COMPLETE - Full understanding achieved of DuckDB's projection pushdown system.

**Key Discovery**: DuckDB provides complete nested field projection information through the `ColumnIndex` structure:

```cpp
struct ColumnIndex {
    idx_t index;                        // Primary column index
    vector<ColumnIndex> child_indexes;  // Nested field information
};
```

**Table Function Integration**:
- `TableFunctionInitInput` contains `vector<ColumnIndex> column_indexes`
- Set `projection_pushdown = true` to receive projection information
- Each `ColumnIndex` can have recursive `child_indexes` for nested field access

**Parquet Implementation Pattern** (`extension/parquet/parquet_reader.cpp`):
```cpp
unique_ptr<ColumnReader> CreateReaderRecursive(
    ClientContext &context,
    const vector<ColumnIndex> &indexes,  // ← Nested field info!
    const ParquetColumnSchema &schema
) {
    if (indexes.empty()) {
        // Read all children (SELECT *)
        for (child_index in schema.children) {
            children[child_index] = CreateReaderRecursive(context, indexes, schema.children[child_index]);
        }
    } else {
        // Read only specific children (projection pushdown)
        for (i in indexes) {
            auto child_index = indexes[i].GetPrimaryIndex();
            children[child_index] = CreateReaderRecursive(
                context,
                indexes[i].GetChildIndexes(),  // ← Recursive nested access!
                schema.children[child_index]
            );
        }
    }
}
```

**Query Examples**:
- `SELECT user.name FROM table` → `ColumnIndex{index: user_col, child_indexes: [ColumnIndex{index: name_field}]}`
- `SELECT user FROM table` → `ColumnIndex{index: user_col, child_indexes: []}`
- `SELECT *` → Empty `column_indexes` vector

### 12.2 COMPLETED: Recursive JSON Schema System ✅

**Implementation Status**: COMPLETE - Proper recursive JSON schema system implemented.

**Major Achievement**: Replaced flawed `JsonRootType` enum with proper recursive JSON semantics:

```rust
enum JsonValueType {
    Null, Boolean, Number, String,
    Array(Box<JsonValueType>),      // Recursive array elements
    Object(Vec<JsonField>),         // Object with named fields
}

struct JsonPath {
    segments: Vec<JsonPathSegment>, // [Field("users"), ArrayWildcard, Field("name")]
}
```

**Path Generation Examples**:
- `users[*].name` → `[Field("users"), ArrayWildcard, Field("name")]`
- `company.departments[*].teams[*].members[*].role` → 8-level deep recursive path
- `metadata.version` → `[Field("metadata"), Field("version")]`
- Root array `[*].id` → `[ArrayWildcard, Field("id")]`

**Capabilities Achieved**:
- ✅ **Arbitrary nesting depth**: Handles 4+ levels correctly
- ✅ **Mixed structures**: Objects + arrays + primitives in same JSON
- ✅ **Multiple arrays**: Processes all arrays in JSON file
- ✅ **Primitive arrays**: Smart "value" column generation for `numbers[*]`, `strings[*]`
- ✅ **Root-level arrays**: Direct flattening without parent wrapper
- ✅ **Type detection**: Numbers, strings, booleans, objects, arrays
- ✅ **Edge cases**: Empty structures, nulls, heterogeneous arrays

**Test Results**: Comprehensive test suite validates recursive schema system handles complex JSON structures correctly.

## 13. Current Status and Next Steps

### 13.1 COMPLETED Major Milestones ✅

**✅ Recursive JSON Schema System**:
- Proper JSON semantics with recursive `JsonValueType` representation
- Perfect path generation for streaming optimization
- Handles arbitrary complexity: nested objects, multiple arrays, mixed structures
- Comprehensive test coverage for edge cases

**✅ DuckDB Integration**:
- Projection pushdown enabled and working
- Column index information accessible
- Table function properly registered and functional

**✅ Foundation for Streaming**:
- Generated JSON paths show exactly what to stream
- Type information available for parsing decisions
- Ready for memory-efficient implementation

### 13.2 Remaining Implementation Tasks

**🔧 Critical Issues to Fix**:
1. **Duplicate Column Names**: Implement path-based prefixing (e.g., `users_id`, `products_id`)
2. **Number Type Handling**: Fix scientific notation display for integers
3. **Streaming Reader Implementation**: Use generated paths for efficient JSON parsing
4. **Memory Optimization**: Only parse required JSON segments based on projection

**🎯 Next Phase Goals**:
- Implement true streaming that only parses projected JSON paths
- Add column name conflict resolution with path prefixes
- Optimize memory usage for large JSON files
- Performance testing vs default DuckDB JSON reader

**📊 Testing Priorities**:
- Memory usage comparison tests
- Performance benchmarks on large files
- Complex nested structure validation
- Edge case robustness testing

### 13.3 Architecture Achievement

The extension now has a **solid recursive foundation** that properly represents JSON semantics and generates the exact paths needed for efficient streaming. The hardcoded assumptions and flawed enum system have been completely replaced with a proper recursive schema system that can handle any JSON complexity level.
