#!/usr/bin/env python3
"""
Memory usage comparison test between DuckDB's default JSON reader and streaming JSON reader.
"""

import duckdb
import psutil
import os
import time
import json
from pathlib import Path

def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / (1024 * 1024)

def test_default_json_reader(filename):
    """Test DuckDB's default JSON reader and measure memory usage."""
    print(f"\n=== Testing Default JSON Reader with {filename} ===")
    
    if not Path(filename).exists():
        print(f"File {filename} does not exist. Skipping test.")
        return None, None, None
    
    file_size = Path(filename).stat().st_size / (1024 * 1024)
    print(f"File size: {file_size:.1f} MB")
    
    conn = duckdb.connect()
    
    # Install and load JSON extension
    try:
        conn.execute("INSTALL json")
        conn.execute("LOAD json")
    except:
        pass  # May already be installed
    
    initial_memory = get_memory_usage()
    print(f"Initial memory: {initial_memory:.1f} MB")
    
    start_time = time.time()
    
    try:
        # Try to read the JSON file with default reader
        # First, let's try to read just the users array
        result = conn.execute(f"""
            SELECT COUNT(*) as user_count 
            FROM read_json('{filename}', format='auto')
        """).fetchone()
        
        peak_memory = get_memory_usage()
        end_time = time.time()
        
        print(f"Peak memory: {peak_memory:.1f} MB")
        print(f"Memory increase: {peak_memory - initial_memory:.1f} MB")
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        print(f"Result: {result}")
        
        return peak_memory - initial_memory, end_time - start_time, result
        
    except Exception as e:
        end_time = time.time()
        peak_memory = get_memory_usage()
        print(f"ERROR: {e}")
        print(f"Peak memory before error: {peak_memory:.1f} MB")
        print(f"Memory increase: {peak_memory - initial_memory:.1f} MB")
        print(f"Time before error: {end_time - start_time:.2f} seconds")
        
        return peak_memory - initial_memory, end_time - start_time, None

def test_streaming_json_reader(filename):
    """Test our streaming JSON reader and measure memory usage."""
    print(f"\n=== Testing Streaming JSON Reader with {filename} ===")
    
    if not Path(filename).exists():
        print(f"File {filename} does not exist. Skipping test.")
        return None, None, None
    
    file_size = Path(filename).stat().st_size / (1024 * 1024)
    print(f"File size: {file_size:.1f} MB")
    
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    
    # Load our streaming JSON reader extension
    try:
        conn.execute('LOAD "./build/debug/streaming_json_reader.duckdb_extension"')
    except Exception as e:
        print(f"Failed to load streaming JSON reader: {e}")
        return None, None, None
    
    initial_memory = get_memory_usage()
    print(f"Initial memory: {initial_memory:.1f} MB")
    
    start_time = time.time()
    
    try:
        # Test our streaming reader (single parameter for now)
        result = conn.execute(f"""
            SELECT COUNT(*) as record_count
            FROM streaming_json_reader('{filename}')
        """).fetchone()
        
        peak_memory = get_memory_usage()
        end_time = time.time()
        
        print(f"Peak memory: {peak_memory:.1f} MB")
        print(f"Memory increase: {peak_memory - initial_memory:.1f} MB")
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        print(f"Result: {result}")
        
        return peak_memory - initial_memory, end_time - start_time, result
        
    except Exception as e:
        end_time = time.time()
        peak_memory = get_memory_usage()
        print(f"ERROR: {e}")
        print(f"Peak memory before error: {peak_memory:.1f} MB")
        print(f"Memory increase: {peak_memory - initial_memory:.1f} MB")
        print(f"Time before error: {end_time - start_time:.2f} seconds")
        
        return peak_memory - initial_memory, end_time - start_time, None

def run_memory_comparison_tests():
    """Run comprehensive memory comparison tests."""
    print("Memory Usage Comparison: Default JSON Reader vs Streaming JSON Reader")
    print("=" * 80)
    
    test_files = [
        "test_small.json",
        "test_medium.json", 
        "test_large.json"
    ]
    
    results = []
    
    for filename in test_files:
        if not Path(filename).exists():
            print(f"\nSkipping {filename} - file not found")
            continue
            
        print(f"\n{'='*60}")
        print(f"TESTING: {filename}")
        print(f"{'='*60}")
        
        # Test default reader
        default_memory, default_time, default_result = test_default_json_reader(filename)
        
        # Test streaming reader  
        streaming_memory, streaming_time, streaming_result = test_streaming_json_reader(filename)
        
        # Compare results
        if default_memory is not None and streaming_memory is not None:
            memory_savings = default_memory - streaming_memory
            memory_savings_percent = (memory_savings / default_memory) * 100 if default_memory > 0 else 0
            
            print(f"\n--- COMPARISON for {filename} ---")
            print(f"Default reader memory: {default_memory:.1f} MB")
            print(f"Streaming reader memory: {streaming_memory:.1f} MB")
            print(f"Memory savings: {memory_savings:.1f} MB ({memory_savings_percent:.1f}%)")
            
            if default_time and streaming_time:
                time_diff = default_time - streaming_time
                print(f"Default reader time: {default_time:.2f}s")
                print(f"Streaming reader time: {streaming_time:.2f}s")
                print(f"Time difference: {time_diff:.2f}s")
        
        results.append({
            'file': filename,
            'default_memory': default_memory,
            'streaming_memory': streaming_memory,
            'default_time': default_time,
            'streaming_time': streaming_time,
            'default_result': default_result,
            'streaming_result': streaming_result
        })
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    for result in results:
        if result['default_memory'] and result['streaming_memory']:
            savings = result['default_memory'] - result['streaming_memory']
            savings_pct = (savings / result['default_memory']) * 100
            print(f"{result['file']}: {savings:.1f} MB saved ({savings_pct:.1f}%)")
        elif result['streaming_memory'] and not result['default_memory']:
            print(f"{result['file']}: Default reader failed, streaming reader used {result['streaming_memory']:.1f} MB")
        else:
            print(f"{result['file']}: Could not compare")

if __name__ == "__main__":
    # First, check if test files exist
    print("Checking for test files...")
    
    required_files = ["test_small.json", "test_medium.json"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"Missing test files: {missing_files}")
        print("Run 'python generate_test_data.py' first to generate test files.")
        exit(1)
    
    # Check if extension is built
    if not Path("./build/debug/streaming_json_reader.duckdb_extension").exists():
        print("Streaming JSON reader extension not found.")
        print("Run 'make debug' first to build the extension.")
        exit(1)
    
    run_memory_comparison_tests()
