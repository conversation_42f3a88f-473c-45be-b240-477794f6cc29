#!/usr/bin/env python3

import duckdb
import json
import os

def test_root_level_array():
    """Test JSON file that is a direct array at root level"""
    print("🔍 Test: Root Level Array")
    
    test_data = [
        {"id": 1, "name": "<PERSON>", "score": 95.5},
        {"id": 2, "name": "<PERSON>", "score": 87.2},
        {"id": 3, "name": "<PERSON>", "score": 92.8}
    ]
    
    with open("test_root_array.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_root_array.json')").fetchall()
        print(f"✅ Root array result: {result}")
        print(f"   Expected: Direct array flattening without parent object")
        
    finally:
        if os.path.exists("test_root_array.json"):
            os.remove("test_root_array.json")

def test_primitive_arrays():
    """Test arrays containing primitive values (not objects)"""
    print("\n🔍 Test: Primitive Arrays")
    
    test_data = {
        "numbers": [1, 2, 3, 4, 5],
        "strings": ["apple", "banana", "cherry"],
        "booleans": [True, False, True],
        "mixed": [1, "hello", True, 3.14],
        "metadata": {"count": 4}
    }
    
    with open("test_primitives.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_primitives.json')").fetchall()
        print(f"✅ Primitive arrays result: {result}")
        print(f"   Expected: How should primitive arrays be flattened?")
        
    finally:
        if os.path.exists("test_primitives.json"):
            os.remove("test_primitives.json")

def test_empty_structures():
    """Test empty arrays and objects"""
    print("\n🔍 Test: Empty Structures")
    
    test_data = {
        "empty_array": [],
        "empty_object": {},
        "users": [
            {"id": 1, "name": "Alice", "tags": []},
            {"id": 2, "name": "Bob", "profile": {}}
        ]
    }
    
    with open("test_empty.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_empty.json')").fetchall()
        print(f"✅ Empty structures result: {result}")
        print(f"   Expected: Graceful handling of empty arrays/objects")
        
    finally:
        if os.path.exists("test_empty.json"):
            os.remove("test_empty.json")

def test_null_values():
    """Test null values in various positions"""
    print("\n🔍 Test: Null Values")
    
    test_data = {
        "users": [
            {"id": 1, "name": "Alice", "email": "<EMAIL>", "phone": None},
            {"id": 2, "name": "Bob", "email": None, "phone": "555-0123"},
            {"id": 3, "name": None, "email": "<EMAIL>", "phone": "555-0456"}
        ],
        "metadata": None
    }
    
    with open("test_nulls.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_nulls.json')").fetchall()
        print(f"✅ Null values result: {result}")
        print(f"   Expected: Proper null handling in flattened structure")
        
    finally:
        if os.path.exists("test_nulls.json"):
            os.remove("test_nulls.json")

def test_heterogeneous_arrays():
    """Test arrays with different object structures"""
    print("\n🔍 Test: Heterogeneous Arrays")
    
    test_data = {
        "events": [
            {"type": "login", "user": "alice", "timestamp": "2024-01-01T10:00:00Z"},
            {"type": "purchase", "user": "bob", "amount": 29.99, "product": "widget"},
            {"type": "logout", "user": "alice", "session_duration": 3600},
            {"type": "error", "message": "Connection failed", "code": 500}
        ]
    }
    
    with open("test_heterogeneous.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_heterogeneous.json')").fetchall()
        print(f"✅ Heterogeneous arrays result: {result}")
        print(f"   Expected: Union of all fields from different object shapes")
        
    finally:
        if os.path.exists("test_heterogeneous.json"):
            os.remove("test_heterogeneous.json")

def test_very_deep_nesting():
    """Test extremely deep nesting to stress test recursion"""
    print("\n🔍 Test: Very Deep Nesting")
    
    test_data = {
        "level1": {
            "level2": {
                "level3": {
                    "level4": {
                        "level5": {
                            "level6": [
                                {
                                    "level7": {
                                        "level8": {
                                            "final_value": "deep_data",
                                            "final_number": 42
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    }
    
    with open("test_very_deep.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_very_deep.json')").fetchall()
        print(f"✅ Very deep nesting result: {result}")
        print(f"   Expected: 8-level deep path: level1.level2...level8.final_value")
        
    finally:
        if os.path.exists("test_very_deep.json"):
            os.remove("test_very_deep.json")

if __name__ == "__main__":
    print("🧪 Testing Edge Cases for Recursive JSON Schema")
    print("=" * 55)
    
    test_root_level_array()
    test_primitive_arrays()
    test_empty_structures()
    test_null_values()
    test_heterogeneous_arrays()
    test_very_deep_nesting()
    
    print("\n" + "=" * 55)
    print("🎯 Edge Case Test Summary:")
    print("- Root level arrays: 🔍")
    print("- Primitive arrays: 🔍") 
    print("- Empty structures: 🔍")
    print("- Null values: 🔍")
    print("- Heterogeneous arrays: 🔍")
    print("- Very deep nesting: 🔍")
