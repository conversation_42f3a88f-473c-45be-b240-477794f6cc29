#!/usr/bin/env python3
"""
Comprehensive Test Suite for Streaming JSON Reader Extension

This test suite validates all the critical improvements made to the extension:
- Error handling and validation
- Unicode support
- Resource management
- Generic JSON structure support
- Security and safety features
"""

import pytest
import duckdb
import tempfile
import json
import os
import time
from pathlib import Path


class TestStreamingJSONReader:
    """Comprehensive test suite for the streaming JSON reader extension."""
    
    @pytest.fixture
    def conn(self):
        """Create a DuckDB connection with the extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "./build/debug/streaming_json_reader.duckdb_extension"')
        return conn
    
    def create_temp_json(self, content, encoding='utf-8'):
        """Helper to create temporary JSON files."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding=encoding) as f:
            if isinstance(content, (dict, list)):
                json.dump(content, f, ensure_ascii=False)
            else:
                f.write(content)
            return f.name


class TestGenericJSONSupport(TestStreamingJSONReader):
    """Test that the extension works with any JSON structure, not just hard-coded schemas."""
    
    def test_simple_objects(self, conn):
        """Test various simple object structures."""
        test_cases = [
            {'name': 'Alice', 'age': 30},
            {'product': 'Widget', 'price': 19.99, 'in_stock': True},
            {'id': 1, 'tags': ['red', 'blue'], 'metadata': {'created': '2023-01-01'}},
            {'empty_object': {}, 'null_field': None, 'zero': 0},
        ]
        
        for i, data in enumerate(test_cases):
            temp_file = self.create_temp_json(data)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Test case {i}: Expected 1 row"
                assert isinstance(result[0][0], str), f"Test case {i}: Expected string result"
                # Verify the JSON is preserved
                parsed = json.loads(result[0][0])
                assert parsed == data, f"Test case {i}: JSON content mismatch"
            finally:
                os.unlink(temp_file)
    
    def test_array_structures(self, conn):
        """Test various array structures."""
        test_cases = [
            [1, 2, 3, 4, 5],
            ['apple', 'banana', 'cherry'],
            [{'id': 1}, {'id': 2}, {'id': 3}],
            [True, False, None, 42, 'mixed'],
        ]
        
        for i, data in enumerate(test_cases):
            temp_file = self.create_temp_json(data)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Array test {i}: Expected 1 row"
                parsed = json.loads(result[0][0])
                assert parsed == data, f"Array test {i}: Content mismatch"
            finally:
                os.unlink(temp_file)
    
    def test_primitive_values(self, conn):
        """Test primitive JSON values."""
        test_cases = [
            ('string', '"hello world"'),
            ('number', '42'),
            ('float', '3.14159'),
            ('boolean_true', 'true'),
            ('boolean_false', 'false'),
            ('null', 'null'),
        ]
        
        for name, content in test_cases:
            temp_file = self.create_temp_json(content)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Primitive test {name}: Expected 1 row"
                assert result[0][0] == content, f"Primitive test {name}: Content mismatch"
            finally:
                os.unlink(temp_file)


class TestErrorHandling(TestStreamingJSONReader):
    """Test comprehensive error handling and validation."""
    
    def test_file_not_found(self, conn):
        """Test handling of non-existent files."""
        with pytest.raises(Exception) as exc_info:
            conn.execute('SELECT * FROM streaming_json_reader("/nonexistent/file.json")').fetchall()
        assert "does not exist" in str(exc_info.value).lower()
    
    def test_malformed_json(self, conn):
        """Test handling of malformed JSON."""
        malformed_cases = [
            '{"name": "Alice", "age":}',  # Missing value
            '{"name": "Alice", "age": 30,}',  # Trailing comma
            '{"name": "Alice", "age": 30',  # Unclosed object
            '{"name": Alice, "age": 30}',  # Unquoted string
            '[1, 2, 3,]',  # Trailing comma in array
            '{"nested": {"unclosed": true}',  # Unclosed nested object
        ]
        
        for i, content in enumerate(malformed_cases):
            temp_file = self.create_temp_json(content)
            try:
                with pytest.raises(Exception) as exc_info:
                    conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                # Verify it's a JSON parsing error, not a crash
                assert "error" in str(exc_info.value).lower()
            finally:
                os.unlink(temp_file)
    
    def test_empty_and_whitespace_files(self, conn):
        """Test handling of empty and whitespace-only files."""
        empty_cases = [
            '',  # Completely empty
            '   ',  # Spaces only
            '\n\t\r  \n',  # Various whitespace
        ]
        
        for i, content in enumerate(empty_cases):
            temp_file = self.create_temp_json(content)
            try:
                with pytest.raises(Exception) as exc_info:
                    conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert "error" in str(exc_info.value).lower()
            finally:
                os.unlink(temp_file)


class TestUnicodeSupport(TestStreamingJSONReader):
    """Test comprehensive Unicode and encoding support."""
    
    def test_international_characters(self, conn):
        """Test various international character sets."""
        unicode_cases = [
            {'name': 'José', 'city': 'São Paulo'},  # Portuguese
            {'name': '张三', 'city': '北京'},  # Chinese
            {'name': 'محمد', 'city': 'الرياض'},  # Arabic
            {'name': 'Владимир', 'city': 'Москва'},  # Russian
            {'name': 'Γιάννης', 'city': 'Αθήνα'},  # Greek
        ]
        
        for i, data in enumerate(unicode_cases):
            temp_file = self.create_temp_json(data)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Unicode test {i}: Expected 1 row"
                parsed = json.loads(result[0][0])
                assert parsed == data, f"Unicode test {i}: Content mismatch"
            finally:
                os.unlink(temp_file)
    
    def test_emojis_and_symbols(self, conn):
        """Test emoji and special symbol support."""
        emoji_cases = [
            {'message': 'Hello 👋 World 🌍', 'reaction': '😀'},
            {'symbols': '©®™€£¥§¶†‡•…‰‹›""'''},
            {'math': '∑∏∆∇∂∫√∞≠≤≥±×÷'},
            {'arrows': '←→↑↓↔↕⇐⇒⇑⇓⇔⇕'},
        ]
        
        for i, data in enumerate(emoji_cases):
            temp_file = self.create_temp_json(data)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Emoji test {i}: Expected 1 row"
                parsed = json.loads(result[0][0])
                assert parsed == data, f"Emoji test {i}: Content mismatch"
            finally:
                os.unlink(temp_file)
    
    def test_escape_sequences(self, conn):
        """Test JSON escape sequences."""
        escape_cases = [
            {'text': 'Line1\nLine2\tTabbed'},
            {'quotes': 'He said "Hello" to her'},
            {'backslash': 'Path\\to\\file'},
            {'unicode': '\u0048\u0065\u006C\u006C\u006F'},  # "Hello"
        ]
        
        for i, data in enumerate(escape_cases):
            temp_file = self.create_temp_json(data)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Escape test {i}: Expected 1 row"
                parsed = json.loads(result[0][0])
                assert parsed == data, f"Escape test {i}: Content mismatch"
            finally:
                os.unlink(temp_file)


class TestResourceManagement(TestStreamingJSONReader):
    """Test resource management and safety features."""
    
    def test_file_size_limits(self, conn):
        """Test file size limit enforcement."""
        # Create a large JSON file (should exceed 100MB limit)
        large_file_path = '/tmp/large_test.json'
        try:
            with open(large_file_path, 'w') as f:
                f.write('[')
                for i in range(500000):  # 500K entries should exceed 100MB
                    if i > 0:
                        f.write(',')
                    f.write(f'{{"id": {i}, "data": "' + 'x' * 200 + '"}')
                f.write(']')
            
            # Verify file is large enough
            file_size = os.path.getsize(large_file_path)
            assert file_size > 100 * 1024 * 1024, "Test file should be > 100MB"
            
            # Should be rejected
            with pytest.raises(Exception) as exc_info:
                conn.execute(f'SELECT * FROM streaming_json_reader("{large_file_path}")').fetchall()
            assert "too large" in str(exc_info.value).lower()
            
        finally:
            if os.path.exists(large_file_path):
                os.remove(large_file_path)
    
    def test_normal_file_sizes(self, conn):
        """Test that normal-sized files work correctly."""
        # Create a reasonably sized file (< 1MB)
        data = [{'id': i, 'data': 'x' * 100} for i in range(1000)]
        temp_file = self.create_temp_json(data)
        
        try:
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            assert len(result) == 1, "Normal file should work"
            parsed = json.loads(result[0][0])
            assert len(parsed) == 1000, "Should contain all 1000 items"
        finally:
            os.unlink(temp_file)


class TestAdvancedScenarios(TestStreamingJSONReader):
    """Test advanced and edge case scenarios."""

    def test_deeply_nested_structures(self, conn):
        """Test deeply nested JSON structures."""
        # Create a deeply nested object
        nested = {'level': 1}
        current = nested
        for i in range(2, 20):  # 19 levels deep
            current['nested'] = {'level': i}
            current = current['nested']

        temp_file = self.create_temp_json(nested)
        try:
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            assert len(result) == 1, "Deeply nested should work"
            parsed = json.loads(result[0][0])
            assert parsed['level'] == 1, "Should preserve structure"
        finally:
            os.unlink(temp_file)

    def test_wide_objects(self, conn):
        """Test objects with many fields."""
        # Create an object with 1000 fields
        wide_object = {f'field_{i}': f'value_{i}' for i in range(1000)}

        temp_file = self.create_temp_json(wide_object)
        try:
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            assert len(result) == 1, "Wide object should work"
            parsed = json.loads(result[0][0])
            assert len(parsed) == 1000, "Should preserve all fields"
            assert parsed['field_500'] == 'value_500', "Should preserve field values"
        finally:
            os.unlink(temp_file)

    def test_mixed_data_types(self, conn):
        """Test complex mixed data type scenarios."""
        complex_data = {
            'string': 'hello',
            'number': 42,
            'float': 3.14159,
            'boolean': True,
            'null_value': None,
            'array': [1, 'two', 3.0, True, None],
            'object': {
                'nested_string': 'world',
                'nested_array': [{'deep': 'value'}]
            },
            'empty_array': [],
            'empty_object': {},
            'unicode': '🌟 Unicode test 测试 тест',
            'large_string': 'x' * 10000,  # 10KB string
        }

        temp_file = self.create_temp_json(complex_data)
        try:
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            assert len(result) == 1, "Complex data should work"
            parsed = json.loads(result[0][0])
            assert parsed == complex_data, "Should preserve complex structure"
        finally:
            os.unlink(temp_file)

    def test_special_json_values(self, conn):
        """Test special JSON values and edge cases."""
        special_cases = [
            {'very_large_number': 9007199254740991},  # Max safe integer
            {'very_small_number': -9007199254740991},
            {'scientific_notation': 1.23e-10},
            {'zero_values': {'zero_int': 0, 'zero_float': 0.0}},
            {'boundary_strings': {'empty': '', 'single_char': 'a'}},
        ]

        for i, data in enumerate(special_cases):
            temp_file = self.create_temp_json(data)
            try:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                assert len(result) == 1, f"Special case {i} should work"
                parsed = json.loads(result[0][0])
                assert parsed == data, f"Special case {i} should preserve values"
            finally:
                os.unlink(temp_file)


class TestPerformanceAndStress(TestStreamingJSONReader):
    """Test performance characteristics and stress scenarios."""

    def test_medium_sized_files(self, conn):
        """Test files in the 1-10MB range."""
        # Create a 5MB file
        large_array = [{'id': i, 'data': 'x' * 1000} for i in range(5000)]

        temp_file = self.create_temp_json(large_array)
        try:
            file_size = os.path.getsize(temp_file)
            assert 1024*1024 < file_size < 50*1024*1024, "Should be 1-50MB"

            start_time = time.time()
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            end_time = time.time()

            assert len(result) == 1, "Should return 1 row"
            assert end_time - start_time < 5.0, "Should complete within 5 seconds"

            parsed = json.loads(result[0][0])
            assert len(parsed) == 5000, "Should contain all items"
        finally:
            os.unlink(temp_file)

    def test_concurrent_access(self, conn):
        """Test that multiple queries can run without interference."""
        # Create multiple test files
        files = []
        try:
            for i in range(5):
                data = {'file_id': i, 'data': f'content_{i}'}
                temp_file = self.create_temp_json(data)
                files.append(temp_file)

            # Run multiple queries
            results = []
            for temp_file in files:
                result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
                results.append(result)

            # Verify all results
            for i, result in enumerate(results):
                assert len(result) == 1, f"File {i} should return 1 row"
                parsed = json.loads(result[0][0])
                assert parsed['file_id'] == i, f"File {i} should have correct ID"
        finally:
            for temp_file in files:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)


if __name__ == '__main__':
    # Run the test suite
    pytest.main([__file__, '-v', '-s'])
