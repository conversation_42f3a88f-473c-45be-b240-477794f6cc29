#!/usr/bin/env python3

import duckdb
import json
import os

def test_basic_array_unnesting():
    """Test basic array unnesting - users array flattened into rows"""
    print("🔍 Test 1: Basic Array Unnesting")
    
    test_data = {
        "users": [
            {"id": 1, "name": "<PERSON>", "email": "<EMAIL>"},
            {"id": 2, "name": "<PERSON>", "email": "<EMAIL>"},
            {"id": 3, "name": "<PERSON>", "email": "<EMAIL>"}
        ]
    }
    
    with open("test_basic.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_basic.json')").fetchall()
        print(f"✅ Basic unnesting result: {result}")
        print(f"   Expected: 3 rows with id, name, email columns")
        
        # Test projection
        names = conn.execute("SELECT name FROM streaming_json_reader('test_basic.json')").fetchall()
        print(f"✅ Name projection: {names}")
        
    finally:
        if os.path.exists("test_basic.json"):
            os.remove("test_basic.json")

def test_nested_object_unnesting():
    """Test unnesting nested objects - profile.age, profile.location"""
    print("\n🔍 Test 2: Nested Object Unnesting")
    
    test_data = {
        "employees": [
            {
                "id": 1,
                "name": "Alice",
                "profile": {
                    "age": 30,
                    "location": "NYC",
                    "skills": ["Python", "SQL"]
                }
            },
            {
                "id": 2, 
                "name": "Bob",
                "profile": {
                    "age": 25,
                    "location": "SF",
                    "skills": ["JavaScript", "React"]
                }
            }
        ]
    }
    
    with open("test_nested.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_nested.json')").fetchall()
        print(f"✅ Nested object result: {result}")
        print(f"   Expected: Flattened profile.age, profile.location columns")
        
    finally:
        if os.path.exists("test_nested.json"):
            os.remove("test_nested.json")

def test_multiple_arrays():
    """Test multiple arrays in same JSON - should handle both users and products"""
    print("\n🔍 Test 3: Multiple Arrays (Column Name Conflicts)")
    
    test_data = {
        "metadata": {"version": "1.0", "created": "2024-01-01"},
        "users": [
            {"id": 1, "name": "Alice", "type": "admin"},
            {"id": 2, "name": "Bob", "type": "user"}
        ],
        "products": [
            {"id": 101, "name": "Widget", "price": 19.99},
            {"id": 102, "name": "Gadget", "price": 29.99}
        ]
    }
    
    with open("test_multi.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        # This should show the duplicate column name issue
        try:
            result = conn.execute("SELECT * FROM streaming_json_reader('test_multi.json')").fetchall()
            print(f"✅ Multi-array result: {result}")
        except Exception as e:
            print(f"⚠️  Expected error (duplicate columns): {e}")
            print(f"   This shows we need to implement column name prefixing")
        
    finally:
        if os.path.exists("test_multi.json"):
            os.remove("test_multi.json")

def test_deeply_nested_structure():
    """Test deeply nested JSON - company.departments[].teams[].members[]"""
    print("\n🔍 Test 4: Deeply Nested Structure")
    
    test_data = {
        "company": {
            "name": "TechCorp",
            "departments": [
                {
                    "name": "Engineering",
                    "teams": [
                        {
                            "name": "Backend",
                            "members": [
                                {"name": "Alice", "role": "Senior Dev"},
                                {"name": "Bob", "role": "Junior Dev"}
                            ]
                        },
                        {
                            "name": "Frontend", 
                            "members": [
                                {"name": "Charlie", "role": "UI Dev"}
                            ]
                        }
                    ]
                }
            ]
        }
    }
    
    with open("test_deep.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_deep.json')").fetchall()
        print(f"✅ Deep nesting result: {result}")
        print(f"   Expected: Flattened paths like company.departments[*].teams[*].members[*].name")
        
    finally:
        if os.path.exists("test_deep.json"):
            os.remove("test_deep.json")

def test_mixed_types_and_arrays():
    """Test mixed primitive types and nested arrays"""
    print("\n🔍 Test 5: Mixed Types and Arrays")
    
    test_data = {
        "title": "Data Export",
        "timestamp": "2024-01-01T10:00:00Z",
        "count": 42,
        "active": True,
        "records": [
            {"id": 1, "value": 10.5, "tags": ["important", "urgent"]},
            {"id": 2, "value": 20.3, "tags": ["normal"]},
            {"id": 3, "value": 15.7, "tags": ["review", "pending", "high-priority"]}
        ]
    }
    
    with open("test_mixed.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_mixed.json')").fetchall()
        print(f"✅ Mixed types result: {result}")
        print(f"   Expected: Top-level primitives + flattened records array")
        
    finally:
        if os.path.exists("test_mixed.json"):
            os.remove("test_mixed.json")

def test_array_of_primitives():
    """Test array of primitive values (not objects)"""
    print("\n🔍 Test 6: Array of Primitives")
    
    test_data = {
        "numbers": [1, 2, 3, 4, 5],
        "strings": ["apple", "banana", "cherry"],
        "metadata": {"source": "test"}
    }
    
    with open("test_primitives.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    try:
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        result = conn.execute("SELECT * FROM streaming_json_reader('test_primitives.json')").fetchall()
        print(f"✅ Primitive arrays result: {result}")
        print(f"   Expected: How should arrays of primitives be handled?")
        
    finally:
        if os.path.exists("test_primitives.json"):
            os.remove("test_primitives.json")

if __name__ == "__main__":
    print("🧪 Testing Recursive JSON Schema System")
    print("=" * 50)
    
    test_basic_array_unnesting()
    test_nested_object_unnesting() 
    test_multiple_arrays()
    test_deeply_nested_structure()
    test_mixed_types_and_arrays()
    test_array_of_primitives()
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("- Basic array unnesting: ✅")
    print("- Nested object unnesting: 🔍 (shows recursive paths)")
    print("- Multiple arrays: ⚠️ (duplicate column names)")
    print("- Deep nesting: 🔍 (complex recursive paths)")
    print("- Mixed types: 🔍 (primitives + arrays)")
    print("- Primitive arrays: 🔍 (edge case handling)")
