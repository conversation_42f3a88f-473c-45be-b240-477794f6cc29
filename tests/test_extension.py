#!/usr/bin/env python3

import duckdb
import json
import os

# Create test data
test_data = {
    "users": [
        {"id": 1, "name": "<PERSON>", "email": "<EMAIL>"},
        {"id": 2, "name": "<PERSON>", "email": "<EMAIL>"},
        {"id": 3, "name": "<PERSON>", "email": "<EMAIL>"}
    ]
}

# Write test data to file
with open("test_data.json", "w") as f:
    json.dump(test_data, f, indent=2)

# Test the extension
try:
    # Create connection with unsigned extensions enabled
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})

    # Load the extension
    conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
    print("✅ Extension loaded successfully")
    
    # Test basic functionality
    print("\n🔍 Testing basic JSON reading:")
    result = conn.execute("SELECT * FROM streaming_json_reader('test_data.json')").fetchall()
    print(f"Result: {result}")

    # Test column projection
    print("\n🔍 Testing column projection:")
    result = conn.execute("SELECT name FROM streaming_json_reader('test_data.json')").fetchall()
    print(f"Name only: {result}")

    # Test specific field selection
    print("\n🔍 Testing specific field selection:")
    result = conn.execute("SELECT id, email FROM streaming_json_reader('test_data.json')").fetchall()
    print(f"ID and email: {result}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

finally:
    # Clean up
    if os.path.exists("test_data.json"):
        os.remove("test_data.json")
