#!/usr/bin/env python3
"""
Advanced Tests: Security Vulnerability Testing

This module tests for security vulnerabilities in the streaming JSON reader:
1. Memory exhaustion attacks (deep nesting, wide objects, large strings)
2. Performance degradation attacks (pathological fan-out, complexity bombs)
3. Correctness issues (data corruption, incorrect parsing)
4. Resource leaks (memory, file handles, threads)
"""

import pytest
import duckdb
import json
import psutil
import os
import tempfile
import time
import threading
import signal
from pathlib import Path


class TestSecurityVulnerabilities:
    """Test suite for security vulnerabilities and attack vectors."""
    
    @pytest.fixture(scope="class")
    def duckdb_connection(self):
        """Create a DuckDB connection with the streaming JSON reader extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        # Load the extension
        extension_path = "../build/debug/streaming_json_reader.duckdb_extension"
        if not Path(extension_path).exists():
            pytest.skip(f"Extension not found at {extension_path}. Run 'make debug' first.")

        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    def get_memory_usage(self):
        """Get current memory usage in MB."""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / (1024 * 1024)
    
    def create_memory_bomb_json(self, bomb_type="deep_nesting", intensity=1000):
        """Create JSON designed to exhaust memory."""
        if bomb_type == "deep_nesting":
            # Create extremely deep nesting to trigger stack overflow
            data = {"leaf": "data"}
            for i in range(intensity):
                data = {f"level_{i}": data}
            return {"users": [data]}
            
        elif bomb_type == "wide_object":
            # Create object with massive number of fields
            user = {}
            for i in range(intensity):
                user[f"field_{i:06d}"] = f"value_{i}"
            return {"users": [user]}
            
        elif bomb_type == "string_bomb":
            # Create massive strings
            huge_string = "A" * intensity * 1000  # intensity KB per string
            return {
                "users": [
                    {
                        "id": 1,
                        "name": huge_string,
                        "bio": huge_string,
                        "description": huge_string,
                        "notes": huge_string
                    }
                ]
            }
            
        elif bomb_type == "array_bomb":
            # Create massive arrays
            users = []
            for i in range(intensity):
                users.append({
                    "id": i,
                    "name": f"User_{i}",
                    "data": "X" * 1000  # 1KB per user
                })
            return {"users": users}
            
        elif bomb_type == "nested_array_bomb":
            # Create nested arrays with exponential growth
            def create_nested_array(depth, width):
                if depth <= 0:
                    return [{"data": f"leaf_{i}"} for i in range(width)]
                return [create_nested_array(depth-1, width) for _ in range(width)]
            
            return {"users": create_nested_array(5, intensity)}
    
    def create_complexity_bomb_json(self, bomb_type="fanout_explosion"):
        """Create JSON designed to cause performance degradation."""
        if bomb_type == "fanout_explosion":
            # One user with massive number of projects
            projects = []
            for i in range(10000):
                projects.append({
                    "id": i,
                    "name": f"Project_{i}",
                    "description": "A" * 100,
                    "metadata": {
                        "created": f"2024-01-{(i % 30) + 1:02d}",
                        "tags": [f"tag_{j}" for j in range(10)]
                    }
                })
            
            return {
                "users": [{
                    "id": 1,
                    "name": "SuperUser",
                    "projects": projects
                }]
            }
            
        elif bomb_type == "recursive_structure":
            # Create recursive-like structure that could confuse parsers
            def create_recursive_level(depth):
                if depth <= 0:
                    return {"end": True}
                
                return {
                    "level": depth,
                    "self_ref": create_recursive_level(depth - 1),
                    "data": "X" * 100
                }
            
            return {"users": [create_recursive_level(100)]}
            
        elif bomb_type == "hash_collision":
            # Create many fields with similar names to potentially trigger hash collisions
            user = {}
            base_name = "field_"
            for i in range(10000):
                # Create field names that might collide in hash tables
                field_name = f"{base_name}{i:04d}"
                user[field_name] = f"value_{i}"
            
            return {"users": [user]}
    
    def create_data_corruption_json(self):
        """Create JSON that might cause data corruption or incorrect parsing."""
        return {
            "users": [
                {
                    "id": 1,
                    "name": "Alice",
                    "data": "\x00\x01\x02\x03\x04\x05",  # Binary data
                    "unicode": "\u0000\uFFFF\uD800\uDFFF",  # Edge case Unicode
                    "numbers": "123.456.789",  # Invalid number format
                    "json_in_string": '{"nested": "json", "in": "string"}',
                    "escape_sequences": "\\n\\t\\r\\\\\\\"\\/'",
                    "very_long_field_name_that_might_cause_buffer_overflow_or_other_issues_when_processing_field_names_in_the_json_parser": "value"
                },
                {
                    "id": 2,
                    "name": "Bob",
                    "mixed_types": [1, "string", True, None, {"nested": "object"}],
                    "boundary_values": {
                        "max_int": 9223372036854775807,
                        "min_int": -9223372036854775808,
                        "large_float": 1.7976931348623157e+308,
                        "small_float": 2.2250738585072014e-308
                    }
                }
            ]
        }
    
    def test_memory_exhaustion_attacks(self, duckdb_connection):
        """Test resistance to memory exhaustion attacks."""
        print("\n=== Testing Memory Exhaustion Attacks ===")
        
        attack_types = [
            ("deep_nesting", 500),
            ("wide_object", 5000),
            ("string_bomb", 100),  # 100KB strings
            ("array_bomb", 1000),
        ]
        
        for attack_type, intensity in attack_types:
            print(f"Testing {attack_type} attack (intensity: {intensity})")
            
            try:
                bomb_data = self.create_memory_bomb_json(attack_type, intensity)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(bomb_data, f)
                    temp_file = f.name
                
                try:
                    mem_before = self.get_memory_usage()
                    start_time = time.time()
                    
                    # Set a timeout to prevent hanging
                    def timeout_handler(signum, frame):
                        raise TimeoutError("Query timed out")
                    
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(30)  # 30 second timeout
                    
                    try:
                        result = duckdb_connection.execute(
                            f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                        ).fetchone()
                        
                        signal.alarm(0)  # Cancel timeout
                        
                        end_time = time.time()
                        mem_after = self.get_memory_usage()
                        
                        execution_time = end_time - start_time
                        mem_diff = mem_after - mem_before
                        
                        print(f"  {attack_type}: SUCCESS - {result[0]} rows, {execution_time:.2f}s, {mem_diff:.1f}MB")
                        
                        # Memory usage should be reasonable despite attack
                        assert mem_diff < 1000, f"Memory usage too high: {mem_diff:.1f}MB"
                        assert execution_time < 25, f"Execution time too long: {execution_time:.2f}s"
                        
                    except TimeoutError:
                        signal.alarm(0)
                        print(f"  {attack_type}: TIMEOUT - Query took too long (potential DoS)")
                        pytest.fail(f"Timeout vulnerability in {attack_type} attack")
                        
                except Exception as e:
                    print(f"  {attack_type}: FAILED - {str(e)[:100]}...")
                    # Failures should be graceful, not crashes
                    error_str = str(e).lower()
                    assert "segmentation fault" not in error_str, "Segmentation fault detected!"
                    assert "out of memory" not in error_str, "Unhandled out of memory!"
                    
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  {attack_type}: SETUP FAILED - {str(e)[:100]}...")
                continue
    
    def test_performance_degradation_attacks(self, duckdb_connection):
        """Test resistance to performance degradation attacks."""
        print("\n=== Testing Performance Degradation Attacks ===")
        
        attack_types = ["fanout_explosion", "recursive_structure", "hash_collision"]
        
        for attack_type in attack_types:
            print(f"Testing {attack_type} attack")
            
            try:
                bomb_data = self.create_complexity_bomb_json(attack_type)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(bomb_data, f)
                    temp_file = f.name
                
                try:
                    start_time = time.time()
                    cpu_before = psutil.cpu_percent(interval=0.1)
                    
                    # Set timeout for performance attacks
                    signal.signal(signal.SIGALRM, lambda s, f: None)
                    signal.alarm(60)  # 60 second timeout
                    
                    try:
                        result = duckdb_connection.execute(
                            f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                        ).fetchone()
                        
                        signal.alarm(0)
                        
                        end_time = time.time()
                        cpu_after = psutil.cpu_percent(interval=0.1)
                        
                        execution_time = end_time - start_time
                        cpu_diff = cpu_after - cpu_before
                        
                        print(f"  {attack_type}: SUCCESS - {result[0]} rows, {execution_time:.2f}s, CPU: {cpu_diff:.1f}%")
                        
                        # Performance should not degrade exponentially
                        assert execution_time < 45, f"Performance degradation: {execution_time:.2f}s"
                        
                    except TimeoutError:
                        signal.alarm(0)
                        print(f"  {attack_type}: TIMEOUT - Performance attack successful")
                        pytest.fail(f"Performance vulnerability in {attack_type} attack")
                        
                except Exception as e:
                    print(f"  {attack_type}: FAILED - {str(e)[:100]}...")
                    
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  {attack_type}: SETUP FAILED - {str(e)[:100]}...")
                continue
    
    def test_data_corruption_attacks(self, duckdb_connection):
        """Test for data corruption vulnerabilities."""
        print("\n=== Testing Data Corruption Attacks ===")
        
        try:
            corruption_data = self.create_data_corruption_json()
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(corruption_data, f, ensure_ascii=False)
                temp_file = f.name
            
            try:
                # Test basic query
                result = duckdb_connection.execute(
                    f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                ).fetchone()
                
                print(f"Basic query: SUCCESS - {result[0]} rows")
                
                # Test data retrieval
                data_result = duckdb_connection.execute(
                    f"SELECT * FROM streaming_json_reader('{temp_file}')"
                ).fetchall()
                
                print(f"Data retrieval: SUCCESS - {len(data_result)} rows retrieved")
                
                # Verify data integrity (no corruption)
                for row in data_result:
                    for value in row:
                        if value is not None:
                            # Check for obvious corruption signs
                            assert isinstance(value, str), f"All values should be strings, got {type(value)}"
                            # Check for buffer overflow indicators
                            assert len(value) < 1000000, f"Value too long, possible buffer overflow: {len(value)}"
                
                print("Data integrity: PASSED")
                
            finally:
                os.unlink(temp_file)
                
        except Exception as e:
            print(f"Data corruption test failed: {e}")
            pytest.fail(f"Data corruption vulnerability detected: {e}")
    
    def test_resource_leak_attacks(self, duckdb_connection):
        """Test for resource leak vulnerabilities."""
        print("\n=== Testing Resource Leak Attacks ===")
        
        # Create a test file
        test_data = {"users": [{"id": i, "name": f"User_{i}"} for i in range(100)]}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            initial_memory = self.get_memory_usage()
            initial_fds = len(os.listdir('/proc/self/fd')) if os.path.exists('/proc/self/fd') else 0
            
            # Perform many rapid queries to test for leaks
            for i in range(50):
                try:
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    # Occasionally check for resource accumulation
                    if i % 10 == 0:
                        current_memory = self.get_memory_usage()
                        memory_growth = current_memory - initial_memory
                        
                        if memory_growth > 200:  # 200MB growth
                            print(f"Potential memory leak detected at iteration {i}: {memory_growth:.1f}MB growth")
                            break
                            
                except Exception as e:
                    print(f"Query {i} failed: {str(e)[:50]}...")
                    continue
            
            final_memory = self.get_memory_usage()
            final_fds = len(os.listdir('/proc/self/fd')) if os.path.exists('/proc/self/fd') else 0
            
            memory_growth = final_memory - initial_memory
            fd_growth = final_fds - initial_fds
            
            print(f"Resource usage after 50 queries:")
            print(f"  Memory growth: {memory_growth:.1f}MB")
            print(f"  File descriptor growth: {fd_growth}")
            
            # Check for resource leaks
            assert memory_growth < 100, f"Memory leak detected: {memory_growth:.1f}MB growth"
            assert fd_growth < 10, f"File descriptor leak detected: {fd_growth} FDs"
            
        finally:
            os.unlink(temp_file)
    
    def test_concurrent_attack_resistance(self, duckdb_connection):
        """Test resistance to concurrent attacks."""
        print("\n=== Testing Concurrent Attack Resistance ===")
        
        # Create attack files
        attack_data = self.create_memory_bomb_json("wide_object", 1000)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(attack_data, f)
            temp_file = f.name
        
        try:
            results = []
            errors = []
            
            def attack_thread():
                try:
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    results.append(result[0])
                except Exception as e:
                    errors.append(str(e))
            
            # Launch concurrent attacks
            threads = []
            for i in range(5):
                thread = threading.Thread(target=attack_thread)
                threads.append(thread)
                thread.start()
            
            # Wait for all attacks to complete
            for thread in threads:
                thread.join(timeout=30)
            
            print(f"Concurrent attacks completed: {len(results)} successes, {len(errors)} errors")
            
            # System should remain stable under concurrent load
            assert len(results) + len(errors) == 5, "Some threads didn't complete"
            
        finally:
            os.unlink(temp_file)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
