#!/usr/bin/env python3
"""
Advanced Tests: Performance Characteristics

This module tests for performance-related issues in the streaming JSON reader:
1. Context replication overhead: O(parent_fields × child_count)
2. String copying performance with large text fields
3. Query performance degradation with high fan-out ratios
4. Memory allocation patterns during streaming
5. CPU usage vs memory trade-offs
"""

import pytest
import duckdb
import json
import psutil
import os
import tempfile
import time
import threading
from pathlib import Path


class TestPerformanceCharacteristics:
    """Test suite for performance characteristics and bottlenecks."""
    
    @pytest.fixture(scope="class")
    def duckdb_connection(self):
        """Create a DuckDB connection with the streaming JSON reader extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        # Load the extension
        extension_path = "../build/debug/streaming_json_reader.duckdb_extension"
        if not Path(extension_path).exists():
            pytest.skip(f"Extension not found at {extension_path}. Run 'make debug' first.")

        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    def get_memory_usage(self):
        """Get current memory usage in MB."""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / (1024 * 1024)
    
    def get_cpu_percent(self):
        """Get current CPU usage percentage."""
        return psutil.cpu_percent(interval=0.1)
    
    def create_high_fanout_json(self, users=10, projects_per_user=1000):
        """Create JSON with high fan-out ratio (many children per parent)."""
        users_data = []
        for user_id in range(users):
            user = {
                "id": user_id,
                "name": f"User_{user_id}",
                "department": "Engineering",
                "email": f"user{user_id}@example.com",
                "bio": "Standard user biography " * 10,  # Some context to replicate
                "projects": []
            }
            
            # Add many projects per user
            for proj_id in range(projects_per_user):
                project = {
                    "id": f"{user_id}_{proj_id}",
                    "name": f"Project_{user_id}_{proj_id}",
                    "status": "active",
                    "budget": 10000 + (proj_id * 1000),
                    "description": f"Project description for {user_id}_{proj_id}"
                }
                user["projects"].append(project)
            
            users_data.append(user)
        
        return {"users": users_data}
    
    def create_large_string_json(self, users=100, string_size=100000):
        """Create JSON with very large string fields."""
        users_data = []
        for user_id in range(users):
            user = {
                "id": user_id,
                "name": f"User_{user_id}",
                "bio": "A" * string_size,  # Very large string
                "description": "B" * (string_size // 2),
                "notes": "C" * (string_size // 4),
                "projects": [
                    {
                        "name": f"Project_{user_id}",
                        "documentation": "D" * string_size,  # Large project docs
                        "requirements": "E" * (string_size // 2)
                    }
                ]
            }
            users_data.append(user)
        
        return {"users": users_data}
    
    def create_pathological_nesting_json(self, depth=10, width=10):
        """Create JSON with pathological nesting patterns."""
        def create_nested_level(current_depth, max_depth):
            if current_depth >= max_depth:
                return {"data": f"leaf_data_{current_depth}"}
            
            level = {}
            for i in range(width):
                level[f"branch_{i}"] = create_nested_level(current_depth + 1, max_depth)
            
            return level
        
        # Create nested structure within users
        users_data = []
        for user_id in range(5):  # Few users but complex structure
            user = {
                "id": user_id,
                "name": f"User_{user_id}",
                "complex_data": create_nested_level(0, depth),
                "projects": [
                    {
                        "name": f"Project_{user_id}",
                        "nested_config": create_nested_level(0, depth // 2)
                    }
                ]
            }
            users_data.append(user)
        
        return {"users": users_data}
    
    def test_context_replication_overhead(self, duckdb_connection):
        """Test context replication overhead with high fan-out ratios."""
        print("\n=== Testing Context Replication Overhead ===")
        
        # Test different fan-out ratios
        test_cases = [
            (5, 100),    # 5 users, 100 projects each
            (10, 500),   # 10 users, 500 projects each  
            (5, 1000),   # 5 users, 1000 projects each
            (2, 2000),   # 2 users, 2000 projects each
        ]
        
        for users, projects_per_user in test_cases:
            print(f"Testing {users} users with {projects_per_user} projects each")
            
            try:
                fanout_data = self.create_high_fanout_json(users, projects_per_user)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(fanout_data, f)
                    temp_file = f.name
                
                try:
                    mem_before = self.get_memory_usage()
                    start_time = time.time()
                    
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    end_time = time.time()
                    mem_after = self.get_memory_usage()
                    
                    execution_time = end_time - start_time
                    mem_diff = mem_after - mem_before
                    total_projects = users * projects_per_user
                    
                    print(f"  Results: {result[0]} rows, {execution_time:.2f}s, {mem_diff:.1f}MB")
                    print(f"  Performance: {total_projects/execution_time:.0f} projects/sec")
                    
                    # Performance should not degrade exponentially with fan-out
                    max_time = total_projects * 0.001  # 1ms per project max
                    assert execution_time < max_time, f"Performance degradation: {execution_time:.2f}s for {total_projects} projects"
                    
                    # Memory should not grow quadratically
                    max_memory = total_projects * 0.01  # 10KB per project max
                    assert mem_diff < max_memory, f"Memory overhead too high: {mem_diff:.1f}MB"
                    
                except Exception as e:
                    print(f"  FAILED: {str(e)[:100]}...")
                    
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  SETUP FAILED: {str(e)[:100]}...")
                continue
    
    def test_large_string_performance(self, duckdb_connection):
        """Test string copying performance with large text fields."""
        print("\n=== Testing Large String Performance ===")
        
        # Test different string sizes
        string_sizes = [10000, 50000, 100000, 500000]  # 10KB to 500KB strings
        
        for string_size in string_sizes:
            print(f"Testing string size: {string_size} bytes")
            
            try:
                large_string_data = self.create_large_string_json(50, string_size)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(large_string_data, f)
                    temp_file = f.name
                
                try:
                    mem_before = self.get_memory_usage()
                    start_time = time.time()
                    
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    end_time = time.time()
                    mem_after = self.get_memory_usage()
                    
                    execution_time = end_time - start_time
                    mem_diff = mem_after - mem_before
                    
                    print(f"  Results: {result[0]} rows, {execution_time:.2f}s, {mem_diff:.1f}MB")
                    
                    # Performance should not degrade linearly with string size
                    max_time = string_size * 0.00001  # 10μs per byte max
                    assert execution_time < max_time, f"String processing too slow: {execution_time:.2f}s"
                    
                    # Memory usage should be reasonable (not storing all strings)
                    total_string_data = 50 * string_size * 4  # 4 large strings per user
                    max_memory = total_string_data / (1024 * 1024) * 0.1  # 10% of total data
                    assert mem_diff < max_memory, f"String memory usage too high: {mem_diff:.1f}MB"
                    
                except Exception as e:
                    print(f"  FAILED: {str(e)[:100]}...")
                    
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  SETUP FAILED: {str(e)[:100]}...")
                continue
    
    def test_pathological_nesting_performance(self, duckdb_connection):
        """Test performance with pathological nesting patterns."""
        print("\n=== Testing Pathological Nesting Performance ===")
        
        # Test different nesting configurations
        nesting_configs = [
            (5, 5),   # 5 levels deep, 5 branches wide
            (8, 3),   # 8 levels deep, 3 branches wide
            (10, 2),  # 10 levels deep, 2 branches wide
            (3, 10),  # 3 levels deep, 10 branches wide
        ]
        
        for depth, width in nesting_configs:
            print(f"Testing nesting: {depth} levels deep, {width} branches wide")
            
            try:
                nested_data = self.create_pathological_nesting_json(depth, width)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(nested_data, f)
                    temp_file = f.name
                
                try:
                    mem_before = self.get_memory_usage()
                    cpu_before = self.get_cpu_percent()
                    start_time = time.time()
                    
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    end_time = time.time()
                    mem_after = self.get_memory_usage()
                    cpu_after = self.get_cpu_percent()
                    
                    execution_time = end_time - start_time
                    mem_diff = mem_after - mem_before
                    cpu_diff = cpu_after - cpu_before
                    
                    print(f"  Results: {result[0]} rows, {execution_time:.2f}s, {mem_diff:.1f}MB, CPU: {cpu_diff:.1f}%")
                    
                    # Performance should not explode with nesting complexity
                    complexity = depth * width
                    max_time = complexity * 0.1  # 100ms per complexity unit
                    assert execution_time < max_time, f"Nesting performance too slow: {execution_time:.2f}s"
                    
                except Exception as e:
                    print(f"  FAILED: {str(e)[:100]}...")
                    
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  SETUP FAILED: {str(e)[:100]}...")
                continue
    
    def test_memory_allocation_patterns(self, duckdb_connection):
        """Test memory allocation patterns during streaming."""
        print("\n=== Testing Memory Allocation Patterns ===")
        
        # Create a moderately large file
        test_data = self.create_high_fanout_json(20, 100)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            # Monitor memory usage during processing
            memory_samples = []
            
            def memory_monitor():
                for _ in range(50):  # Sample for 5 seconds
                    memory_samples.append(self.get_memory_usage())
                    time.sleep(0.1)
            
            # Start memory monitoring
            monitor_thread = threading.Thread(target=memory_monitor)
            monitor_thread.start()
            
            # Execute query
            result = duckdb_connection.execute(
                f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
            ).fetchone()
            
            # Wait for monitoring to complete
            monitor_thread.join()
            
            if len(memory_samples) > 10:
                min_memory = min(memory_samples)
                max_memory = max(memory_samples)
                memory_variance = max_memory - min_memory
                
                print(f"Memory range: {min_memory:.1f}MB - {max_memory:.1f}MB (variance: {memory_variance:.1f}MB)")
                
                # Memory usage should be relatively stable (streaming behavior)
                assert memory_variance < 200, f"High memory variance suggests non-streaming behavior: {memory_variance:.1f}MB"
            
        finally:
            os.unlink(temp_file)
    
    def test_cpu_vs_memory_tradeoffs(self, duckdb_connection):
        """Test CPU usage vs memory trade-offs."""
        print("\n=== Testing CPU vs Memory Trade-offs ===")
        
        # Create test data with different characteristics
        test_cases = [
            ("high_fanout", self.create_high_fanout_json(10, 200)),
            ("large_strings", self.create_large_string_json(100, 50000)),
            ("deep_nesting", self.create_pathological_nesting_json(6, 4))
        ]
        
        for case_name, test_data in test_cases:
            print(f"Testing {case_name}")
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(test_data, f)
                temp_file = f.name
            
            try:
                mem_before = self.get_memory_usage()
                start_time = time.time()
                
                # Monitor CPU during execution
                cpu_samples = []
                def cpu_monitor():
                    for _ in range(20):
                        cpu_samples.append(psutil.cpu_percent(interval=0.1))
                
                cpu_thread = threading.Thread(target=cpu_monitor)
                cpu_thread.start()
                
                result = duckdb_connection.execute(
                    f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                ).fetchone()
                
                cpu_thread.join()
                end_time = time.time()
                mem_after = self.get_memory_usage()
                
                execution_time = end_time - start_time
                mem_diff = mem_after - mem_before
                avg_cpu = sum(cpu_samples) / len(cpu_samples) if cpu_samples else 0
                
                print(f"  {case_name}: {execution_time:.2f}s, {mem_diff:.1f}MB, {avg_cpu:.1f}% CPU")
                
                # Verify reasonable resource usage
                assert execution_time < 30, f"Execution too slow: {execution_time:.2f}s"
                assert mem_diff < 500, f"Memory usage too high: {mem_diff:.1f}MB"
                assert avg_cpu < 90, f"CPU usage too high: {avg_cpu:.1f}%"
                
            finally:
                os.unlink(temp_file)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
