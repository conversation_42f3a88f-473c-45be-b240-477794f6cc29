extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON><PERSON><PERSON><PERSON><PERSON>, JsonStreamReader};

/// Represents a column index with potential nested field access
#[derive(Debug, Clone)]
struct ColumnIndex {
    index: usize,
    child_indexes: Vec<ColumnIndex>,
}

impl ColumnIndex {
    fn new(index: usize) -> Self {
        Self {
            index,
            child_indexes: Vec::new(),
        }
    }

    fn with_children(index: usize, children: Vec<ColumnIndex>) -> Self {
        Self {
            index,
            child_indexes: children,
        }
    }

    fn has_children(&self) -> bool {
        !self.child_indexes.is_empty()
    }
}

/// Discovered JSON schema information
#[derive(Debug, Clone)]
struct JsonSchema {
    columns: Vec<String>,
    is_array_root: bool,
    nested_object_key: Option<String>, // Key containing the array if not at root
}

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<ColumnIndex>, // Columns requested by the query
}

struct JsonReaderVTab;

// Helper function to discover JSON schema from first element
fn discover_json_schema(
    file_path: &str
) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    let mut columns = Vec::new();
    let mut is_array_root = false;
    let mut nested_object_key = None;

    // Discover JSON structure - support both arrays and objects with top-level fields
    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut found_array = false;

            // First pass: look for arrays to flatten
            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?;
                let field_name_owned = field_name.to_string();

                if let struson::reader::ValueType::Array = json_reader.peek()? {
                    json_reader.begin_array()?;
                    nested_object_key = Some(field_name_owned.clone());

                    // Get schema from first array element
                    if json_reader.has_next()? {
                        if let struson::reader::ValueType::Object = json_reader.peek()? {
                            json_reader.begin_object()?;

                            while json_reader.has_next()? {
                                let column_name = json_reader.next_name()?;
                                columns.push(column_name.to_string());
                                json_reader.skip_value()?; // Skip the value, we just want the field names
                            }

                            json_reader.end_object()?;
                        }
                    }
                    found_array = true;
                    break; // Found our array, stop looking
                } else {
                    // This is a top-level field - add it as a column
                    columns.push(field_name_owned);
                    json_reader.skip_value()?;
                }
            }

            // If no array found, treat all top-level fields as columns
            if !found_array && columns.is_empty() {
                // Re-read to get all top-level fields
                // (This is a simplified approach - in practice we'd need to restart the reader)
                return Err("No suitable JSON structure found - expected object with fields or arrays".into());
            }
        }
        struson::reader::ValueType::Array => {
            // Direct array at root level
            is_array_root = true;
            json_reader.begin_array()?;

            if json_reader.has_next()? {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;

                    while json_reader.has_next()? {
                        let column_name = json_reader.next_name()?;
                        columns.push(column_name.to_string());
                        json_reader.skip_value()?;
                    }

                    json_reader.end_object()?;
                }
            }
        }
        _ => {
            return Err("Expected JSON object or array at root".into());
        }
    }

    if columns.is_empty() {
        return Err("No suitable JSON structure found".into());
    }

    Ok(JsonSchema {
        columns,
        is_array_root,
        nested_object_key,
    })
}

// Helper function to read and flatten JSON arrays generically
fn read_and_flatten_json(
    file_path: &str,
    schema: &JsonSchema,
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // For the first call, read the entire array and return first batch
    let current_element = init_data.current_element.load(Ordering::Relaxed);

    if current_element > 0 {
        // We've already processed some elements, return empty to indicate we're done
        // TODO: Implement proper streaming across multiple calls
        return Ok(vec![]);
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    let columns = &schema.columns;
    let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
    let mut elements_read = 0;
    let max_elements = 100; // Limit to prevent memory issues

    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;

            // Look for the first array field
            while json_reader.has_next()? {
                let _field_name = json_reader.next_name()?;

                if let struson::reader::ValueType::Array = json_reader.peek()? {
                    json_reader.begin_array()?;

                    // Process array elements
                    while json_reader.has_next()? && elements_read < max_elements {
                        if let struson::reader::ValueType::Object = json_reader.peek()? {
                            json_reader.begin_object()?;
                            let mut row_data = vec!["".to_string(); columns.len()];

                            while json_reader.has_next()? {
                                let field_name = json_reader.next_name()?;
                                if let Some(col_idx) = columns.iter().position(|c| c == &field_name) {
                                    // Extract the value for this column
                                    let value = match json_reader.peek()? {
                                        struson::reader::ValueType::String => json_reader.next_string()?,
                                        struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                        struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                        struson::reader::ValueType::Null => {
                                            json_reader.next_null()?;
                                            "null".to_string()
                                        }
                                        _ => {
                                            json_reader.skip_value()?;
                                            "".to_string()
                                        }
                                    };
                                    row_data[col_idx] = value;
                                } else {
                                    json_reader.skip_value()?;
                                }
                            }
                            json_reader.end_object()?;

                            // Add row data to result columns
                            for (col_idx, value) in row_data.into_iter().enumerate() {
                                result_columns[col_idx].push(value);
                            }
                            elements_read += 1;
                        } else {
                            json_reader.skip_value()?;
                            elements_read += 1;
                        }
                    }

                    break; // Found our array, stop looking
                } else {
                    json_reader.skip_value()?;
                }
            }
        }
        struson::reader::ValueType::Array => {
            // Direct array at root level
            json_reader.begin_array()?;

            while json_reader.has_next()? && elements_read < max_elements {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                    let mut row_data = vec!["".to_string(); columns.len()];

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        if let Some(col_idx) = columns.iter().position(|c| c == &field_name) {
                            // Extract the value for this column
                            let value = match json_reader.peek()? {
                                struson::reader::ValueType::String => json_reader.next_string()?,
                                struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                struson::reader::ValueType::Null => {
                                    json_reader.next_null()?;
                                    "null".to_string()
                                }
                                _ => {
                                    json_reader.skip_value()?;
                                    "".to_string()
                                }
                            };
                            row_data[col_idx] = value;
                        } else {
                            json_reader.skip_value()?;
                        }
                    }
                    json_reader.end_object()?;

                    // Add row data to result columns
                    for (col_idx, value) in row_data.into_iter().enumerate() {
                        result_columns[col_idx].push(value);
                    }
                    elements_read += 1;
                } else {
                    json_reader.skip_value()?;
                    elements_read += 1;
                }
            }
        }
        _ => {
            return Err("Expected JSON object or array at root".into());
        }
    }

    // Mark as finished since we read everything in one go
    init_data.finished.store(true, Ordering::Relaxed);
    init_data.current_element.store(elements_read, Ordering::Relaxed);

    Ok(result_columns)
}





impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Discover JSON schema from the file
        let schema = match discover_json_schema(&file_path) {
            Ok(schema) => {
                eprintln!("DEBUG BIND: Discovered schema: {:?}", schema);
                schema
            },
            Err(e) => {
                eprintln!("DEBUG BIND: Schema discovery failed: {}", e);
                // Fallback to generic schema if discovery fails
                JsonSchema {
                    columns: vec!["id".to_string(), "name".to_string(), "value".to_string()],
                    is_array_root: false,
                    nested_object_key: None,
                }
            }
        };

        // Add result columns to DuckDB - let's try adding some as nested types
        for (i, column) in schema.columns.iter().enumerate() {
            eprintln!("DEBUG BIND: Adding column '{}' at index {}", column, i);

            // For testing: make some columns nested types
            if column == "projects" {
                // Try to add as a LIST type for unnest testing
                let list_type = LogicalTypeHandle::list(&LogicalTypeHandle::from(LogicalTypeId::Varchar));
                bind.add_result_column(column, list_type);
                eprintln!("DEBUG BIND: Added '{}' as LIST type", column);
            } else if column == "users" {
                // Try to add as a LIST of STRUCT for nested field access testing
                let struct_type = LogicalTypeHandle::struct_type(&[
                    ("id", LogicalTypeHandle::from(LogicalTypeId::Bigint)),
                    ("name", LogicalTypeHandle::from(LogicalTypeId::Varchar)),
                    ("profile", LogicalTypeHandle::struct_type(&[
                        ("age", LogicalTypeHandle::from(LogicalTypeId::Bigint)),
                        ("email", LogicalTypeHandle::from(LogicalTypeId::Varchar)),
                    ])),
                ]);
                let list_type = LogicalTypeHandle::list(&struct_type);
                bind.add_result_column(column, list_type);
                eprintln!("DEBUG BIND: Added '{}' as LIST of STRUCT type", column);
            } else {
                bind.add_result_column(column, LogicalTypeHandle::from(LogicalTypeId::Varchar));
                eprintln!("DEBUG BIND: Added '{}' as VARCHAR type", column);
            }
        }

        Ok(JsonReaderBindData {
            file_path,
            schema,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Test projection pushdown capabilities
        eprintln!("DEBUG: Testing projection pushdown capabilities");

        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();
        eprintln!("DEBUG: Projected column indices: {:?}", column_indices);
        eprintln!("DEBUG: Number of projected columns: {}", column_indices.len());

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        eprintln!("DEBUG: All available columns: {:?}", bind_data.schema.columns);

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < bind_data.schema.columns.len() {
                    bind_data.schema.columns[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to our ColumnIndex structure
        let projected_columns: Vec<ColumnIndex> = column_indices
            .iter()
            .map(|&idx| ColumnIndex::new(idx as usize))
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);
        eprintln!("DEBUG FUNC: Available columns: {:?}", bind_data.schema.columns);

        // Try to read and flatten the JSON array
        match read_and_flatten_json(&bind_data.file_path, &bind_data.schema, init_data) {
            Ok(rows) => {
                if rows.is_empty() {
                    init_data.finished.store(true, Ordering::Relaxed);
                    output.set_len(0);
                } else {
                    // Fill the output vectors with the row data
                    for (col_idx, column_data) in rows.iter().enumerate() {
                        let vector = output.flat_vector(col_idx);
                        for (row_idx, value) in column_data.iter().enumerate() {
                            let cstring = CString::new(value.as_str())?;
                            vector.insert(row_idx, cstring);
                        }
                    }
                    output.set_len(rows[0].len());
                }
            }
            Err(e) => {
                // Return error as a single row
                let vector = output.flat_vector(0);
                let error_msg = CString::new(format!("Error: {}", e))?;
                vector.insert(0, error_msg);
                output.set_len(1);
                init_data.finished.store(true, Ordering::Relaxed);
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}