# DuckDB Streaming JSON Extension - Phase 2 Implementation

## Context Summary

You are continuing development of a **memory-efficient streaming JSON reader extension** for DuckDB using Rust. The extension has achieved major architectural milestones and is ready for the final implementation phase.

## Current Status ✅

### ✅ **Recursive JSON Schema System - COMPLETE**
- **Proper JSON semantics**: Replaced flawed `JsonRootType` enum with recursive `JsonValueType` system
- **Perfect path generation**: Creates exact streaming paths like `users[*].profile.age`, `company.departments[*].teams[*].members[*].role`
- **Arbitrary complexity support**: Handles nested objects, multiple arrays, mixed structures, primitive arrays
- **Comprehensive testing**: Edge cases validated including root arrays, deep nesting, heterogeneous structures

### ✅ **DuckDB Integration - WORKING**
- **Projection pushdown enabled**: Extension receives column projection information
- **Table function registered**: `streaming_json_reader('file.json')` functional
- **Type mapping**: JSON types correctly mapped to DuckDB types

### ✅ **Foundation for Streaming - READY**
- **Generated JSON paths**: Show exactly what segments to parse for efficiency
- **Type information**: Available for parsing optimization decisions
- **Recursive architecture**: Handles any JSON complexity level

## Critical Issues to Fix 🔧

### 1. **Duplicate Column Names**
**Problem**: Multiple arrays create conflicting column names (e.g., `users.id` and `products.id` both become `id`)
**Solution Needed**: Implement path-based prefixing system (e.g., `users_id`, `products_id`)

### 2. **Number Type Display**
**Problem**: Numbers showing as scientific notation instead of clean integers/floats
**Solution Needed**: Fix DuckDB type mapping and value insertion for numeric types

### 3. **Streaming Implementation**
**Problem**: Current implementation doesn't use generated paths for efficient parsing
**Solution Needed**: Implement true streaming that only parses projected JSON segments

### 4. **Memory Optimization**
**Problem**: Not yet achieving memory efficiency goals
**Solution Needed**: Use projection information to skip unnecessary JSON parsing

## Implementation Tasks 🎯

### **Priority 1: Fix Column Name Conflicts**
```rust
// Current: Both create "id" column
users[*].id → "id"
products[*].id → "id"  // ❌ Conflict!

// Needed: Path-based naming
users[*].id → "users_id" 
products[*].id → "products_id"
```

### **Priority 2: Implement Path-Based Streaming**
Use the generated `JsonPath` structures to only parse required JSON segments:
```rust
// Example: For query "SELECT users_name FROM file"
// Only parse: [Field("users"), ArrayWildcard, Field("name")]
// Skip: All other JSON fields and arrays
```

### **Priority 3: Memory Efficiency Testing**
Create tests comparing memory usage vs default DuckDB JSON reader on large files.

### **Priority 4: Performance Optimization**
Implement streaming reader that processes JSON incrementally without loading entire objects.

## Key Files and Architecture

### **Core Implementation**: `src/lib.rs`
- `JsonValueType`: Recursive JSON type system ✅
- `JsonPath`: Path generation system ✅  
- `FlattenedColumn`: Column representation ✅
- `discover_json_schema()`: Schema analysis ✅
- `read_and_flatten_json()`: **Needs streaming implementation** 🔧

### **Test Files**:
- `tests/test_recursive_schema.py`: Comprehensive functionality tests ✅
- `tests/test_edge_cases.py`: Edge case validation ✅
- Need: Memory usage and performance tests 🔧

### **Documentation**: `design_decisions.md`
- Complete record of architectural decisions ✅
- Current status and next steps documented ✅

## Success Criteria 🏆

1. **✅ Column Name Conflicts Resolved**: No duplicate column errors
2. **✅ Clean Number Display**: Integers show as integers, not scientific notation
3. **✅ Memory Efficiency**: Uses significantly less memory than default JSON reader on large files
4. **✅ Streaming Performance**: Only parses projected JSON paths, skips unnecessary data
5. **✅ Complex Structure Support**: Handles deeply nested and mixed JSON structures correctly

## Technical Approach

### **Column Naming Strategy**
Implement path-based column naming that creates unique names from JSON paths:
- `metadata.version` → `metadata_version`
- `users[*].profile.age` → `users_profile_age`
- Root array `[*].name` → `name` (no prefix needed)

### **Streaming Implementation**
Use the recursive `JsonPath` system to guide JSON parsing:
1. **Schema Discovery**: Generate all possible paths (current ✅)
2. **Projection Mapping**: Map DuckDB column indices to JSON paths
3. **Selective Parsing**: Only parse JSON segments required by query
4. **Memory Efficiency**: Process JSON incrementally, not all at once

### **Integration Points**
- **DuckDB Projection System**: Already integrated ✅
- **Rust JSON Streaming**: Using `struson` crate ✅
- **Type System**: JSON → DuckDB type mapping ✅

## Expected Outcome

A **production-ready streaming JSON extension** that:
- Handles any JSON complexity with proper recursive semantics
- Uses minimal memory by only parsing required JSON paths  
- Provides significant performance improvements over default JSON reader
- Integrates seamlessly with DuckDB's query system
- Supports complex nested field access and array flattening

The recursive foundation is **solid and complete**. Focus on implementing the streaming optimization and fixing the remaining issues to achieve a fully functional, memory-efficient JSON reader extension.
